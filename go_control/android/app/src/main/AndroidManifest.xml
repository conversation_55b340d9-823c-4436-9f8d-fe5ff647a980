<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.gocontrol">
    <!-- 连接到互联网，发送和接收数据 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 访问设备的网络连接状态信息，如是否连接到网络、网络类型等 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- 访问设备的 Wi-Fi 连接状态信息，如 Wi-Fi 是否打开、连接的网络等 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 改变设备的 Wi-Fi 多播状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <!-- 权限允许应用程序保持设备的唤醒状态，以确保在网络通信期间设备不会进入睡眠模式。 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" /> 
    <!-- 指定应用程序需要检查设备是否支持蓝牙低功耗（BLE）功能 -->
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="false" />
    <!-- Android 12新增蓝牙权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!-- Android 11或更低版本的遗留版本 -->
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <!-- Android 9或更低版本的遗留版本 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <!-- 读取和写入的权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
   <application
        android:label="Go Control"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">

        <service
        android:name="com.haberey.flutter.nsd_android.NsdService"
        android:exported="false">
            <intent-filter>
                <action android:name="com.haberey.flutter.nsd_android.NsdService" />
            </intent-filter>
        </service>

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- <data android:scheme="https" /> -->
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

            
    </application>
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <data android:scheme="http" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <category android:name="android.intent.category.DEFAULT" />
            <data android:scheme="https" />
        </intent>
    </queries>

</manifest>
