// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:io';

import 'package:bwee_mod/bwee_mod.dart' as bwee;
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:gocomponent/theme/theme_manager.dart';
import 'package:gocontrol/common/app_observer.dart';
import 'package:gocontrol/common/storage.dart';
import 'package:gocontrol/core/permission.dart';
import 'package:gocontrol/lang/language.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/LoginPage/singin/components/isAuto_login.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';

import 'package:rk_package/rk_package.dart';
import 'package:rk_log/rk_log.dart' as rk_log;

import 'pages/AppSetting/setc.dart';

void main() => AppInit.run();

class AppInit {
  static void run() async {
    runZonedGuarded(
      () async {
        WidgetsFlutterBinding.ensureInitialized();
        rk_log.defaultLogProvier();
        Get.lazyPut(() => AppController());
        Get.lazyPut(() => ThemeManager());
        // bwee.init();

        await initFirst().then((_) => runApp(const MyApp()));
        FlutterError.onError = (FlutterErrorDetails details) {
          print(details.stack);
          final String? path = details.stack
              ?.toString()
              .split('\n')[0]
              .split(" ")
              .last;
          if ('${details.exception}'.contains('No host specified in URI')) {
            return;
          }
          Log.e("代码异常: 信息 = [${details.exception}] , 位置 = [$path]");
        };

        isAutoLogin();
      },  
      (Object error, StackTrace stack) {
        reportError(error, stack);
      },
    );
  }

  // 在 App 初始化之前需要执行的操作
  static Future<void> initFirst() async {
    // 初始化 App 需要的权限
    await AppPermission.init();
    // 本地存储
    await GetStorage.init();
    // 开启监视 App 的前台和后台情况
    WidgetsBinding.instance.addObserver(AppObserver());
    // 设置全局的 http 访问
    HttpOverrides.global = GlobalHttpOverrides();
    // 开始搜索设备
    homCon.initMode();
  }

  // 上报错误信息
  static void reportError(Object error, StackTrace stack) {
    print(stack);
    Log.e("错误信息1：${error.toString()} 错误信息2：${stack.toString()}");
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // 是否是第一次启动 App
    final bool notFirstApp = StorageClass.getStorage('notFirstApp') ?? false;
    final toastBuilder = BotToastInit();
    //
    return ScreenUtilInit(
      designSize: const Size(385, 840),
      minTextAdapt: true,
      splitScreenMode: false,
      builder: (context, child) {
        // 计算状态栏高度
        themeBase.searHeight.value = MediaQuery.of(context).padding.top;
        themeBase.bottomPadding.value = MediaQuery.of(context).padding.bottom;

        return GetMaterialApp(
          getPages: [...Routes.pages, ...bwee.BweeRouteConfig.pages],
          builder: (context, child) {
            return toastBuilder(context, child);
          },
          navigatorObservers: [BotToastNavigatorObserver()],
          initialRoute: notFirstApp ? Routes.homePage : Routes.login,
          locale: _getAppLanguage(),
          localizationsDelegates: [...bwee.localizationsDelegates],
          translations: LanguageClass(),
          fallbackLocale: const Locale('en', 'US'),
          debugShowCheckedModeBanner: false,
          theme: ThemeManager.to.theme,
          themeMode: ThemeManager.to.themeMode,
        );
      },
    );
  }
}

Locale _getAppLanguage() {
  String? lanCode = StorageClass.getStorage('appLanguage');
  String? couCode = StorageClass.getStorage('appCountryCode');
  if (lanCode != null && couCode != null) {
    homCon.appLanguageCode.value = lanCode;
    homCon.appCountryCode.value = couCode;
    return Locale(lanCode, couCode);
  } else {
    if (Get.deviceLocale != null) {
      switch (Get.deviceLocale!.languageCode) {
        case 'de':
          homCon.appLanguageCode.value = 'de';
          homCon.appCountryCode.value = 'DE';
          break;
        case 'en':
          homCon.appLanguageCode.value = 'en';
          homCon.appCountryCode.value = 'US';
          break;
        case 'ja':
          homCon.appLanguageCode.value = 'ja';
          homCon.appCountryCode.value = 'JP';
          break;
        case 'ko':
          homCon.appLanguageCode.value = 'ko';
          homCon.appCountryCode.value = 'KR';
          break;
        case 'ru':
          homCon.appLanguageCode.value = 'ru';
          homCon.appCountryCode.value = 'RU';
          break;
        case 'th':
          homCon.appLanguageCode.value = 'th';
          homCon.appCountryCode.value = 'TH';
          break;
        case 'zh':
          homCon.appLanguageCode.value = 'zh';
          if (Get.deviceLocale!.countryCode == 'TW') {
            homCon.appCountryCode.value = 'TW';
          } else {
            homCon.appCountryCode.value = 'CN';
          }
          break;
      }
    }
    
    return Get.deviceLocale ?? const Locale('en', 'US');
  }
}

class GlobalHttpOverrides extends HttpOverrides {
  //
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
        (X509Certificate cert, String host, int port) => true;
  }
} 
