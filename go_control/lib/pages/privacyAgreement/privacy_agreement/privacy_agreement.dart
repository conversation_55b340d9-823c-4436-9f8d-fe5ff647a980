import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/pages/privacyAgreement/components/privacy_btn.dart';
import 'package:gocontrol/theme/theme.dart';

class PrivacyAgreement extends StatelessWidget {
  const PrivacyAgreement({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度
    double screenWidth = MediaQuery.of(context).size.width;
    return PagesBody(
      scroll: true,
      topbar: const Top2tion(
        title: '法律信息',
      ),
      body:  BodyBox(
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                color: themeBase.primaryColor.value
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 15.sp,
              ),
              child: ListView(
                children: [
                  PrivacyBtn(
                  title: '用户协议',
                    tap: (){
                      Get.toNamed('/userAgreement');
                      Log.d('用户协议');
                    }
                  ),
                  PrivacyBtn(
                  title: '隐私政策',
                    tap: (){
                      Get.toNamed('/privacyPolicy');
                      Log.d('隐私政策');
                    }
                  ),

                ],
              ),

            ),
            Positioned(
              left: 15.sp,
              bottom: 130.sp,
              child: Container(
                width: screenWidth - 15 * 2,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      offset: const Offset(0, 4), 
                      blurRadius: 10, 
                      spreadRadius: 1, 
                    ),
                  ],
                  // 使用渐变
                  // gradient: LinearGradient(
                  //   colors: [
                  //     themeBase.errorColor.value, 
                  //     themeBase.textColor1.value, 
                  //   ],
                  //   begin: Alignment.center, 
                  //   end: Alignment.bottomCenter, 
                  // ),
                  color: themeBase.errorColor.value,
                  borderRadius: BorderRadius.circular(15.sp)
                ),
                height: 70.sp,
                child: Center(
                  child: GestureDetector(
                    child: Text(
                      '撤销授权',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: themeBase.textColor1.value
                      ),
                    ),
                  ),
                ),
              )
            )
          ],
        )
      ),
    );
  }
}