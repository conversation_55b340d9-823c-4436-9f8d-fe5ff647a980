import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/components/page_bodys.dart';
import 'package:gocontrol/pages/home/<USER>/top2tion.dart';
import 'package:gocontrol/theme/theme.dart';
// 用户协议
class UseAgreement extends StatelessWidget {
  const UseAgreement({super.key});

  @override
  Widget build(BuildContext context) {
    return PagesBody(
      scroll: true,
      topbar: const Top2tion(
        title: '用户协议',
      ),
      body: BodyBox(
        child: Container(
          decoration: BoxDecoration(
            color: themeBase.primaryColor.value
          ),
          child: Padding(
            padding: EdgeInsets.all(10.sp),
              child: ListView(
              children: [
                Text(
                  '服务条款',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: themeBase.textColor1.value
                  ),
                ),
                // 用户协议的详细内容
                Text(
                  '\n欢迎使用我们的应用!在使用本应用之前，请您仔细阅读以下用户协议。'
                  '\n\n第一条:协议的接受'
                  '\n您在使用本应用的过程中即表示您接受本协议的所有条款,若不同意本协议的任何条款，请停止使用本应用。'
                  '\n\n第二条:服务内容'
                  '\n本应用为用户提供以下服务:'
                  '\n- 提供信息查询功能'
                  '\n- 提供用户社区交流功能'
                  '\n- 提供个性化推荐服务'
                  '\n\n第三条:用户的权利与义务'
                  '\n1. 用户有权使用本应用提供的各项功能。'
                  '\n2. 用户需自行负责其账户内的信息安全，若因用户自身原因导致的损失由用户自行承担。'
                  '\n3. 用户不得利用本应用进行任何违法活动，若因此产生的后果由用户自负。'
                  '\n\n第四条:隐私政策'
                  '\n我们承诺保护用户的隐私信息,未经用户同意，不会将用户信息提供给第三方。详细的隐私政策请参考《隐私政策》。'
                  '\n\n第五条:协议的修改'
                  '\n我们保留随时修改本协议的权利,修改后的协议会在应用内公布，用户有责任定期查看，继续使用即表示用户接受修改后的协议。'
                  '\n\n第六条:法律适用'
                  '\n本协议的订立、执行和解释均适用中华人民共和国法律。'
                  '\n\n感谢您的理解与支持!',
                  style: TextStyle(
                    fontSize: 16.sp, 
                    color: themeBase.textColor1.value,
                  ),
                  
                ),
                SizedBox(height: 80.h), 
              ],
            ),
          )
        )
      ),
    );
  }
}