import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/theme/theme.dart';

class PrivacyBtn extends StatelessWidget {
  const PrivacyBtn({
    super.key,
    required this.title,
    required this.tap,
  });
  final String title;

  final Function tap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        tap();
      },
      child: Container(
        height: 50.sp,
        margin: EdgeInsets.only(bottom: 10.sp),
        padding: EdgeInsets.symmetric(horizontal: 15.sp),
        decoration: BoxDecoration(
          color: themeBase.secondaryColor.value,
          borderRadius: BorderRadius.circular(15.sp)
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: themeBase.textColor1.value
              ),
            ),
            Text(
              '>',
              style: TextStyle(
                fontSize: 20.sp,
                color: themeBase.textColor1.value
              ),

            )
          ],

        ),
      ),
    );
  }
}