import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:gocomponent/gocomponet.dart';
import 'package:gocontrol/common/take_bgcolor.dart';
import 'package:gocontrol/common/toast.dart';
import 'package:gocontrol/components/go_icon.dart';
import 'package:gocontrol/log.dart';
import 'package:gocontrol/models/class/class_ble.dart';
import 'package:gocontrol/models/class/class_net.dart';
import 'package:gocontrol/components/loading.dart';
import 'package:gocontrol/models/device/a31/a31.dart';
import 'package:gocontrol/models/device/b50/b50.dart';
import 'package:gocontrol/models/device/libre/libre.dart';
import 'package:gocontrol/models/model_class/a1_base.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gocontrol/models/model_class/a2_audio.dart';
import 'package:gocontrol/pages/PlayerPage/components/btm_btn.dart';
import 'package:gocontrol/pages/home/<USER>/home_control.dart';
import 'package:gocontrol/pages/home/<USER>/components/auracast_body.dart';
import 'package:gocontrol/pages/home/<USER>/components/image_hl.dart';
import 'package:gocontrol/pages/home/<USER>/components/name_hl.dart';
import 'package:gocontrol/pages/home/<USER>/components/not_network_icon.dart';
import 'package:gocontrol/pages/home/<USER>/components/room_page.dart';
import 'package:gocontrol/pages/home/<USER>/components/rxtx_body.dart';
import 'package:gocontrol/pages/home/<USER>/components/slider_volume.dart';
import 'package:gocontrol/pages/home/<USER>/components/sound_source.dart';
import 'package:gocontrol/pages/home/<USER>/components/song_name.dart';
import 'package:gocontrol/pages/home/<USER>/components/song_singer.dart';
import 'package:gocontrol/routes/routes.dart';
import 'package:gocontrol/theme/theme.dart';
import 'package:gocontrol/components/atext.dart';
import 'package:reorderables/reorderables.dart';

import 'components/bwee_switch.dart';

class HomeList extends StatelessWidget {
  const HomeList({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => ReorderableSliverList(
        delegate: ReorderableSliverChildBuilderDelegate(
          (context, index) {
            final DeviceBase device = homCon.adevices[index];
            return Obx(
              () => Visibility(
                visible: _netNotble(device),
                child: Column(
                  children: [
                    SizedBox(height: index == 0 ? 6.sp : 0.sp),
                    GestureDetector(
                      onTap: () => _selectToDevice(index),
                      child: Obx(
                        () => Visibility(
                          visible: _displayDevice(device), // 是否显示device
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 180),
                            curve: Curves.easeInOut,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.r),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: _cardColor(index),
                              ),
                            ),
                            margin: EdgeInsets.symmetric(
                              vertical: 6.sp,
                              horizontal: 12.sp,
                            ),
                            alignment: Alignment.topLeft,
                            child: DeviceBody(device: device, inx: index),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: (index + 1) == homCon.adevices.length
                          ? 16.sp
                          : 0.sp,
                    ),
                  ],
                ),
              ),
            );
          },
          // 遍历数量
          childCount: homCon.adevices.length,
        ),
        // 长按拖拽切换位置
        onReorder: (int oldIndex, int newIndex) {
          DeviceBase row = homCon.adevices.removeAt(oldIndex);
          int newAdevInx = homCon.adevInx.value;
          if (oldIndex == newAdevInx) {
            newAdevInx = newIndex;
          } else {
            if (oldIndex < newAdevInx && newIndex >= newAdevInx) {
              newAdevInx--;
            } else if (oldIndex > newAdevInx && newIndex <= newAdevInx) {
              newAdevInx++;
            }
          }
          homCon.adevInx.value = newAdevInx;
          homCon.adevices.insert(newIndex, row);
        },
      ),
    );
  }
}

//
bool _netNotble(DeviceBase device) {
  if (device.origin == Origins.ble) {
    List lt = homCon.adevices.where((e) => e.origin == Origins.net).map((e) {
      return e.name.value;
    }).toList();
    if (lt.contains(device.name.value)) return false;
  }
  return true;
}

// 操控是否显示设备
bool _displayDevice(device) {
  // 网络设备
  if (device is A31 && device.isRoomChild.value) return false;
  if (device is B50 && device.auracastChild.value) return false;
  return true;
}

// 选择设备并跳转页面
void _selectToDevice(int inx) {
  // 设备未连接并且没有名字时不执行任何操作
  var dev = homCon.adevices[inx];
  if (dev.name.value == '') return;
  if (dev.sourceInput.value == '' &&
      dev is AbstractNET &&
      dev.origin == Origins.net) {
    return;
  }
  if (homCon.selectDevice is B50 && dev != homCon.selectDevice) {
    B50 b50 = homCon.selectDevice as B50;
    if (b50.isBLEConnected.value) {
      if (b50.deviceMode.value != 'Up2Cast' &&
          b50.auracastType.value != 2 &&
          b50.auracastType.value != 4) {
        b50.disconnectBLE();
      } else {
        b50.disconnectBLE();
      }
    }
  }
  // 当前选择的设备
  homCon.selectDevice = dev;
  if (homCon.selectDevice is B50) {
    B50 b50 = homCon.selectDevice as B50;
    if (b50.sourceInputList.isEmpty || b50.sourceInput.value == '') {
      b50.connectBLE();
    } else {
      b50.reconnectBLE();
      Log.d('重连');
    }
  }

  DeviceBase device = homCon.selectDevice!;
  // 选择当前第几台设备
  if (homCon.adevInx.value != inx) {
    // 切换选择中设备的索引
    homCon.adevInx.value = inx;
  } else {
    if (device is Libre) {
      if (device.origin != Origins.net) return;
      if (device.updateState.value == 0 || device.updateState.value == 99) {
        Get.toNamed('/player_page');
        device.getNetworkInfo();
        TakeBgColor.getImageDominantColor(device.songImage.value);
        return;
      } else {
        Get.toNamed(Routes.libreUpdateVersionPage);
        return;
      }
    }
    if (device is B50 && !device.isBLEConnected.value) {
      device.reconnectBLE();
      return;
    }
    // 跳转播放页面
    if (device is AbstractNET) {
      if (device is A31) device.initSetting();
      if (device.sourceInput.value == 'FM') {
        device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:FMQ&');
        Get.toNamed(Routes.fmPage);
        return;
      }
      device.getPlaySongList();
      if (device.project.contains('A100') &&
          device.sourceInput.value == 'USBDAC')
        device.sendMsgToSocket('MCU+PAS+RAKOIT:FWD:DSK:TOT&');
      Get.toNamed(Routes.playerPage);
      TakeBgColor.getImageDominantColor(device.songImage.value);
    } else {
      AbstractBLE ble = device as AbstractBLE;
      if (ble.sourceInput.value == 'USBPLAY') {
        if (ble.dataTot.value == 0) {
          ble.sendMsgToBLE(msg: 'DSK:TOT;');
        } else {
          ble.sendMsgToBLE(msg: 'DSK:CUR;');
        }
      }
      if (device is BaseAudio) {
        BaseAudio audio = device as BaseAudio;
        audio.initAudioData();
        Get.toNamed('/ble_page/usbplayPage');
      }
    }
  }
}

List<Color> _cardColor(int inx) {
  if (homCon.adevInx.value == inx) {
    return [
      themeBase.secondaryColor2.value,
      const Color.fromRGBO(24, 50, 80, 1),
    ];
  }
  return [themeBase.secondaryColor.value, themeBase.secondaryColor.value];
}

// 设备的内容
class DeviceBody extends StatelessWidget {
  const DeviceBody({super.key, required this.device, required this.inx});

  final DeviceBase device;
  final int inx;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        child: _getBody(),
      ),
    );
  }

  Widget _getBody() {
    // 设备加载页面
    if (device.name.value == '') return _loadingBody();
    if (device is B50) {
      B50 b50 = device as B50;
      if (b50.isBLEConnected.value &&
          b50.sourceInput.value == '' &&
          b50.sourceInputList.isEmpty) {
        return _loadingBody();
      }
    }

    Widget getBox2() {
      if (device is B50) {
        B50 b50Device = device as B50;
        if (b50Device.showBtm1.value || b50Device.showBtm2.value) {
          return Column(
            children: [
              Obx(
                () => Visibility(
                  visible:
                      !b50Device.showBtm1.value && !b50Device.showBtm2.value,
                  child: SongNameHL(inx),
                ),
              ),
              Obx(
                () => Visibility(
                  visible: b50Device.showBtm1.value,
                  child: Flexible(
                    child: Transform.translate(
                      offset: Offset(0, -1.sp),
                      child: BtmBtn(device: device as B50, id: 1),
                    ),
                  ),
                ),
              ),
              Obx(
                () => Visibility(
                  visible: b50Device.showBtm1.value && b50Device.showBtm2.value,
                  child: SizedBox(height: 5.sp),
                ),
              ),
              Obx(
                () => Visibility(
                  visible: b50Device.showBtm2.value,
                  child: Flexible(
                    child: Transform.translate(
                      offset: Offset(0, -1.sp),
                      child: BtmBtn(device: device as B50, id: 2),
                    ),
                  ),
                ),
              ),
            ],
          );
        }
      }
      return Column(children: [SongNameHL(inx), SongSingerHL(inx)]);
    }

    // 正常页面
    return Stack(
      children: [
        Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.sp),
              child: Column(
                children: [
                  NameHL(inx),
                  Row(
                    children: [
                      SongImage(inx),
                      Flexible(
                        child: Container(
                          height: 60.sp,
                          padding: EdgeInsets.only(left: 12.sp),
                          child: getBox2(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            RxTxBody(inx),
            SliderVolume(inx),
            RoomPage(inx),
            AuracastBodyAndChilds(inx),
            SoundSource(inx),
          ],
        ),
        MultiRoomBtn(inx),
        SetupNetBodyIcon(inx),
        BleDeviceBodyIcon(inx),
        LibreDeviceBodyIcon(inx),
        // BweeSwitch(inx, key: Key(device.name.value)),
      ],
    );
  }

  // 加载部分
  Widget _loadingBody() {
    String name = '';
    if (device is AbstractBLE && device.origin == Origins.ble) {
      AbstractBLE bleDev = device as AbstractBLE;
      String platformName = bleDev.device.platformName;
      name = platformName.substring(0, platformName.length - 4);
    } else {
      name = device.name.value;
    }
    return SizedBox(
      height: 240.sp,
      child: Center(
        child: Loading(
          text: '$name Connecting...',
          color: themeBase.textColor1.value,
        ),
      ),
    );
  }
}

class MultiRoomBtn extends StatelessWidget {
  const MultiRoomBtn(this.inx, {super.key});

  final int inx;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 1.sp,
      right: 6.sp,
      // FF97001B = A97L
      // FF970023 = A97
      child: Obx(
        () => Visibility(
          visible: getRoom(),
          child: GestureDetector(
            onTap: () => showMultiRoom(inx),
            child: Container(
              width: 45.sp,
              height: 45.sp,
              color: Colors.transparent,
              child: GoIcon(
                name: GoIcons.add,
                size: 25.sp,
                color: themeBase.textColor1.value,
              ),
            ),
          ),
        ),
      ),
    );
  }

  bool getRoom() {
    final device = homCon.adevices[inx];
    if (device is Libre) return false;
    if (device is A31 && device.deviceMode.value == 'H50') {
      return false;
    } else if (device is A31 && device.deviceMode.value == 'A98') {
      return false;
    } else {
      return device.origin == Origins.net && inx == homCon.adevInx.value;
    }
  }
}

void showMultiRoom(inx) async {
  AbstractNET device = homCon.adevices[inx] as AbstractNET;
  RxList<DeviceBase> lt = RxList<DeviceBase>([]);
  RxList<String> ltIP = RxList<String>([]);
  final loading = false.obs;

  for (String st in device.roomChildren) {
    if (!ltIP.contains(st)) ltIP.add(st);
  }

  ltIP.add(device.ip);

  // for (DeviceBase dev in homCon.adevices) {
  //   lt.add(dev);
  // }
  lt.addAll(homCon.adevices);
  // if (dev is A31) {
  //   // 其他设备并且不处于master模式
  //   if (device != dev && dev.roomChildren.isEmpty) {
  //     // 其他设备并且不处于子模式
  //     if (!dev.isRoomChild.value) lt.add(dev);
  //     // 当前设备要是包含这个子设备要显示
  //     if (device.roomChildren.contains(dev.ip)) lt.add(dev);
  //   }
  //   // 当前设备直接添加
  //   if (device == dev) lt.add(dev);
  // }

  lt.sort((a, b) {
    if (a is AbstractNET) {
      if (a.ip == device.ip) return -1;
      return 1;
    } else {
      return 1;
    }
  });

  Get.bottomSheet(
    SizedBox(
      height: 355.h,
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 22.sp, bottom: 22.sp),
            child: AText(
              text: 'mulit_room_title'.tr,
              family: TextFamily.bold,
              size: themeBase.subHeadingFont.value,
              color: themeBase.primaryColor.value,
            ),
          ),
          Flexible(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 22.sp),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: ListView.builder(
                      padding: const EdgeInsets.all(0),
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            if (lt[index] is AbstractNET) {
                              _isAddToPlay(device.ip, lt[index], ltIP);
                            } else {
                              AppToast.show(
                                'This device does not support multiple rooms!',
                              );
                            }
                          },
                          child: Opacity(
                            opacity: lt[index] is AbstractNET ? 1 : 0.5,
                            child: Container(
                              height: 44.sp,
                              margin: EdgeInsets.only(bottom: 3.sp),
                              decoration: BoxDecoration(
                                border: index + 1 != lt.length
                                    ? Border(
                                        bottom: BorderSide(
                                          width: 0.1.sp,
                                          color: themeBase.primaryColor.value
                                              .toOpacity(.7),
                                        ),
                                      )
                                    : null,
                              ),
                              child: Row(
                                children: [
                                  Flexible(
                                    flex: 1,
                                    child: Container(
                                      alignment: Alignment.centerLeft,
                                      child: AText(
                                        text: lt[index].name.value,
                                        family: TextFamily.medium,
                                        size:
                                            themeBase.subBodyFont.value + 1.sp,
                                        color: themeBase.primaryColor.value,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 18.sp),
                                  Obx(
                                    () => Container(
                                      width: 21.sp,
                                      height: 21.sp,
                                      // color: Colors,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          6.r,
                                        ),
                                        border: Border.all(
                                          color: themeBase.primaryColor.value
                                              .toOpacity(
                                                index == 0 ? 0.5 : 1,
                                              ),
                                          width: 1.5.sp,
                                        ),
                                      ),
                                      child: Visibility(
                                        visible: () {
                                          final my = lt[index];
                                          if (my is AbstractNET) {
                                            if (my.ip == device.ip) return true;
                                            return ltIP.contains(my.ip);
                                          } else {
                                            return false;
                                          }
                                        }(),
                                        child: GoIcon(
                                          name: GoIcons.tick,
                                          size: 18.sp,
                                          color: themeBase.primaryColor.value
                                              .toOpacity(index == 0 ? 0.5 : 1),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      itemCount: lt.length,
                    ),
                  ),
                  SizedBox(height: 22.sp),
                  GestureDetector(
                    onTap: () async {
                      List iilt = [];
                      for (String st in ltIP) {
                        if (st != device.ip) iilt.add(st);
                      }
                      loading.value = true;
                      await _sendToAll(iilt, device);
                      loading.value = false;
                    },
                    child: Container(
                      width: double.maxFinite,
                      height: 44.sp,
                      margin: EdgeInsets.only(bottom: 22.sp),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: themeBase.primaryColor.value,
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Obx(
                        () => loading.value
                            ? Container(
                                width: 44.sp,
                                height: 44.sp,
                                padding: EdgeInsets.all(8.sp),
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    themeBase.blockColor.value,
                                  ),
                                  strokeWidth: 2.6.sp,
                                ),
                              )
                            : AText(
                                text: 'Confirm'.tr,
                                color: themeBase.textColor1.value,
                                size: themeBase.subBodyFont.value + 1.sp,
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
    backgroundColor: themeBase.textColor1.value,
  );
}

void _isAddToPlay(String st, DeviceBase device, RxList<String> ltIp) {
  if (device is AbstractNET) {
    if (st == device.ip) return;
    if (!ltIp.contains(device.ip)) {
      ltIp.add(device.ip);
    } else {
      ltIp.removeWhere((el) => device.ip == el);
    }
  } else {}
}

Future<void> _sendToAll(List iilt, AbstractNET device) async {
  // 踢出多房间的子项
  for (String iip in device.roomChildren) {
    if (!iilt.contains(iip)) {
      device.slaveKickout(iip);
      Future.delayed(
        const Duration(milliseconds: 100),
        () => device.getMatchingVolumes(),
      );
    }
  }
  // 添加到设备多房间
  for (DeviceBase as in homCon.adevices) {
    if (as is AbstractNET && !(as.isRoomChild.value) && iilt.contains(as.ip)) {
      // Log.d('${as.name} 要添加');
      await Future.delayed(
        const Duration(microseconds: 600),
        () async => await as.joinGroupMaster(device.ip),
      );
    }
  }
  Get.back();
}
