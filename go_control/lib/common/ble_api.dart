
import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:get/get.dart';
import 'package:gocontrol/log.dart';

class BleApi extends GetxController {

  // 蓝牙状态
  final RxBool bleState = false.obs;
  // 本地记录用来防止ble搜索到重复设备
  static final List<ScanResult> scanList = [];
  // 用来控制蓝牙和扫描的进程
  StreamSubscription? bleStream;
  StreamSubscription? scanStream;

  @override
  void onClose() {
    bleStream?.cancel();
    scanStream?.cancel();
    bleStream = null;
    scanStream = null;
    super.onClose();
  }

  @override
  void onInit() async {
    super.onInit();
    // 显示所有数据输入和输出。
    FlutterBluePlus.setLogLevel(
      LogLevel.error, 
      color: true
    );
    // 如果是安卓设备则需要手动唤醒弹窗提示用户打开蓝牙
    if (Platform.isAndroid) {
      try {
        await FlutterBluePlus.turnOn();
      } catch(e) {
        Log.e(e);
        SystemNavigator.pop();
      }
    }
    // 注册蓝牙状态监听
    setBleStateStream();
  }

  // 给蓝牙状态添加一个监听器
  void setBleStateStream() {
    bleStream = FlutterBluePlus.adapterState.listen((BluetoothAdapterState state) {
      if (state == BluetoothAdapterState.on) {
        Log.d('蓝牙连接状态: on');
        bleState.value = true;
      } else if (state == BluetoothAdapterState.off) {
        Log.d('蓝牙连接状态: off');
        bleState.value = false;
      }
    });
  }

  // 监听蓝牙scan到的设备
  void bleScanResults(callback) async {
    scanStream = FlutterBluePlus.onScanResults.listen((results) async {
      if (results.isEmpty || results.last.device.platformName == '') return;
      ScanResult scanResult = results.last;
      Map<int, List<int>> data = scanResult.advertisementData.manufacturerData;
      Log.b('Scan到设备 - ${scanResult.device.platformName} ----- $scanResult ---- RSSI: ${scanResult.rssi}');
      if (scanList.contains(scanResult)) return;
      if (scanResult.rssi < -88) return;

      // 过滤掉是不是Luci的libre设备的BLE
      if (data.keys.contains(16722)) {
        final List<int> info = data[16722] ?? [];
        if (info.length == 12) {
          final haveWifi = info[10] == 1;
          if (haveWifi) return Log.w('过滤掉是不是Luci的libre设备的BLE ${scanResult.device.platformName}');
        }
      }

      callback(scanResult);
      scanList.add(scanResult);
    });
  }

  // 扫描当前外围ble设备
  static Future<void> scan({
    bool searchB50 = false,
    bool searchNET = false,
    bool searchLibre = false,
    bool light = false
  }) async {
    scanList.clear();
    if (FlutterBluePlus.isScanningNow) {
      Log.d('中断上一次扫描');
      await FlutterBluePlus.stopScan();
      await Future.delayed(const Duration(seconds: 1));
    }
    Log.d('开始扫描');

    FlutterBluePlus.startScan(
      androidUsesFineLocation: Platform.isAndroid,
      // withKeywords: ['B50', 'BP50', 'B50SE', 'SoundSystem'],
      withServices: [
        if (light) Guid('bae55b96-7d19-458d-970c-50613d801bc9')
      ],
      withMsd: [
        if (searchB50) MsdFilter(16722),
        if (searchNET) MsdFilter(20556)
      ],
      withServiceData: [
        if (searchLibre) ...[
          ServiceDataFilter(
            Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1'),
            data: [0, 0, 0]
          ),
          ...List.generate(5, (index) {
            return ServiceDataFilter(
              Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1'),
              data: [0, 17, (index + 1)]
            );
          }),
          ServiceDataFilter(
            Guid('29320bdb-b9b4-53cd-aae9-b1da527728d1'),
            data: [0, 18, 4]
          ),
          
        ],
        // 
      ]
    );
  }

  // 停止扫描
  static Future<void> offScan() async {
    if (!FlutterBluePlus.isScanningNow) return;
    await FlutterBluePlus.stopScan();
    Log.d('停止扫描');
  }

}