// import 'package:ffmpeg_kit_flutter_full_gpl/ffmpeg_kit.dart';

class AudioConvert {
  static Future<void> audioConvert2mp3ByFFmpeg(List<String> params)async{
    // final inputFilePath = params[0];
    // final outputFilePath = params[1];
    // try {
    //   await FFmpegKit.executeAsync(
    //     '-y -i "$inputFilePath" -acodec libmp3lame -q:a 0 "$outputFilePath"',
    //   ).then((session) async {
    //     final returnCode = await session.getReturnCode();
    //     print(returnCode);
    //     if (returnCode?.getValue() != 0) {
    //       final code = await session.getFailStackTrace();
    //       print('code $code');
    //     }
    //   });
    // } catch (e) {
    //   throw Exception('ffmpeg命令执行失败: $e');
    // }
  }
}
