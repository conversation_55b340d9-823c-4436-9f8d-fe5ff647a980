import 'package:flutter/material.dart';
import 'package:rk_package/rk_package.dart';
import 'package:sim2/src/configs/kv_store.dart';

import 'package:sim2/src/ui/common/sim2_icons.dart';
import 'package:sim2/src/ui/common/sim2_nav.dart';
import 'package:sim2/src/ui/radio_gage/controller/arylic_controller.dart';

import '../../configs/local.dart';
import '../business/setting_container.dart';
import '../common/common_background.dart';
import 'controller/aroha_controller.dart';


import 'controller/local_music_con.dart';
import 'radio/spotify_page/get.dart';
import 'routes.dart';

class RadioPage extends StatelessWidget {
  const RadioPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CommonBackground(
      appBar: Sim2AppBar(title: Local.radioPage.music_sources),
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
        child: Setting<PERSON>lock(
          children: [
            SettingBlockChild.customIcon(
              title: Local.radioPage.local_music,
              customIcon: Container(
                margin: EdgeInsets.only(right: 40.w),
                child: Transform.translate(
                  offset: Offset(0, 5.w),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.pinkAccent, Colors.lightBlueAccent],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                    width: 60.w,
                    height: 60.w,
                  ),
                ),
              ),
              navigateTo: () {
                localMusicCon.init();
                Get.toNamed(RadioPageRoutes.localMusicPage);
              },
            ),
            // SettingBlockChild.navigation(
            //   title: 'NAS PLAY',
            //   iconPath: Assets.ASSETS_ICONS_ARYLIC_ICON_SVG,
            //   navigateTo: () async {
            //     nasRadio.discoverClients();
            //     nasRadio.currentSort.value =
            //         await kvStore.getInt('NAS:SORT') ?? 0;
            //     Get.toNamed(RadioPageRoutes.upnpRadio);
            //   },
            // ),
            SettingBlockChild.navigation(
              title: 'ArylicRadio',
              iconPath: Assets.ASSETS_ICONS_ARYLIC_ICON_SVG,
              navigateTo: () async {
                final bool yes =
                    await kvStore.getBool('ArylicRadioPrivacy') ?? false;
                if (yes) {
                  arylicRadioMusic.getSummaryCode();
                  Get.toNamed('/arylicRadio/public');
                } else {
                  Get.toNamed(RadioPageRoutes.privacyPolicy);
                }
              },
            ),
            SettingBlockChild.navigation(
              title: 'YouRadio',
              iconPath: Assets.ASSETS_ICONS_AROHA_ICON22_SVG,
              navigateTo: () {
                arohaController.getAllBrand();
                Get.toNamed('/arohaRadio');
              },
            ),
            SettingBlockChild.navigation(
              title: 'Spotify',
              iconPath: Assets.ASSETS_ICONS_PRIMARY_LOGO_GREEN_PMS_U_SVG,
              navigateTo: () async {
                SpotifyGetCon spotifyGetCon = Get.put(SpotifyGetCon());
                spotifyGetCon.iHave.value = await canLaunchUrl(
                  Uri.parse('spotify://'),
                );
                Get.toNamed(RadioPageRoutes.spotifyGet);
              },
            ),
            SettingBlockChild.customIcon(
              title: 'TIDAL',
              customIcon: Container(
                margin: EdgeInsets.only(right: 40.w),
                child: Transform.translate(
                  offset: Offset(0, 5.w),
                  child: ExtendedImage.asset(
                    Assets.ASSETS_IMAGES_TIDAL_LOGO_PNG,
                    width: 60.w,
                    height: 60.w,
                  ),
                ),
              ),
              navigateTo: () async {
                SpotifyGetCon spotifyGetCon = Get.put(SpotifyGetCon());
                spotifyGetCon.tidalIHave.value = await canLaunchUrl(
                  Uri.parse('tidal://'),
                );
                Get.toNamed(RadioPageRoutes.tidalGet);
              },
            ),
          ],
        ),
      ),
    );
  }
}
