// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// TLGetXRouterConfigGenerator
// **************************************************************************

// ignore_for_file: depend_on_referenced_packages, prefer_const_constructors
// ignore_for_file: prefer_const_literals_to_create_immutables
// ignore_for_file: unnecessary_parenthesis

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:ui' as _i35;

import 'package:base_common/base_common.dart' as _i30;
import 'package:flutter/material.dart' as _i4;
import 'package:get/get.dart';
import 'package:rk_package/rk_package.dart' as _i25;
import 'package:sim2/src/ui/device/device_general_settings/binding.dart'
    as _i17;
import 'package:sim2/src/ui/device/device_general_settings/device_report_issue/binding.dart'
    as _i19;
import 'package:sim2/src/ui/device/device_general_settings/time_zone_first/binding.dart'
    as _i15;
import 'package:sim2/src/ui/device/device_general_settings/time_zone_second/binding.dart'
    as _i13;
import 'package:sim2/src/ui/device/device_play_view/binding.dart' as _i11;
import 'package:sim2/src/ui/device/device_upgrading/binding.dart' as _i9;
import 'package:sim2/src/ui/device/sound_settings/binding.dart' as _i7;
import 'package:sim2/src/ui/g_cast/g_cast_info/binding.dart' as _i33;
import 'package:sim2/src/ui/g_cast/g_cast_ready/binding.dart' as _i38;
import 'package:sim2/src/ui/g_cast/set_g_cast/binding.dart' as _i36;
import 'package:sim2/src/ui/set_wifi/ble_discovery/binding.dart' as _i26;
import 'package:sim2/src/ui/set_wifi/connecting_device_status/binding.dart'
    as _i31;
import 'package:sim2/src/ui/set_wifi/password_input/binding.dart' as _i28;
import 'package:sim2/src/ui/set_wifi/wifi_discovery/binding.dart' as _i23;
import 'package:sim2/src/ui/setting/set_language/binding.dart' as _i21;
import 'package:sim2/src/ui/test_page/binding.dart' as _i5;
import 'package:tl_getx_router_gen_annotations/tl_getx_router_gen_annotations.dart'
    as _i40;

import '../ui/device/device_general_settings/device_report_issue/view.dart'
    as _i20;
import '../ui/device/device_general_settings/time_zone_first/view.dart' as _i16;
import '../ui/device/device_general_settings/time_zone_second/view.dart'
    as _i14;
import '../ui/device/device_general_settings/view.dart' as _i18;
import '../ui/device/device_list/device_list.dart' as _i1;
import '../ui/device/device_play_view/view.dart' as _i12;
import '../ui/device/device_upgrading/view.dart' as _i10;
import '../ui/device/sound_settings/view.dart' as _i8;
import '../ui/g_cast/g_cast_info/view.dart' as _i34;
import '../ui/g_cast/g_cast_ready/view.dart' as _i39;
import '../ui/g_cast/set_g_cast/view.dart' as _i37;
import '../ui/home/<USER>' as _i3;
import '../ui/set_wifi/ble_discovery/view.dart' as _i27;
import '../ui/set_wifi/connecting_device_status/view.dart' as _i32;
import '../ui/set_wifi/password_input/view.dart' as _i29;
import '../ui/set_wifi/wifi_discovery/view.dart' as _i24;
import '../ui/setting/set_language/view.dart' as _i22;
import '../ui/setting/setting.dart' as _i2;
import '../ui/test_page/view.dart' as _i6;

mixin Sim2RouteConfigMixin {
  static final pages = [
    GetPage<dynamic>(
      name: RouteNames.homeView,
      bindings: [_i1.DeviceListBinding(), _i2.SettingBinding()],
      page: () => _i3.HomeView(key: (Get.arguments?['key'] as _i4.Key?)),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.testPageView,
      bindings: [_i5.TestPageBinding()],
      page: () => _i6.TestPageView(key: (Get.arguments?['key'] as _i4.Key?)),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.soundSettingsView,
      bindings: [_i7.SoundSettingsBinding()],
      page: () => _i8.SoundSettingsView(
        ip: (Get.arguments?['ip'] as String),
        key: (Get.arguments?['key'] as _i4.Key?),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.deviceUpgradingView,
      bindings: [_i9.DeviceUpgradingBinding()],
      page: () => _i10.DeviceUpgradingView(
        key: (Get.arguments?['key'] as _i4.Key?),
        deviceIP: (Get.arguments?['deviceIP'] as String),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.playView,
      bindings: [_i11.DevicePlayViewBinding()],
      page: () => _i12.DevicePlayViewView(
        key: (Get.arguments?['key'] as _i4.Key?),
        deviceIP: (Get.arguments?['deviceIP'] as String),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.timeZoneSecondView,
      bindings: [_i13.TimeZoneSecondBinding()],
      page: () => _i14.TimeZoneSecondView(
        key: (Get.arguments?['key'] as _i4.Key?),
        index: (Get.arguments?['index'] as int),
        selectedTimeZone: (Get.arguments?['selectedTimeZone'] as String),
        onTimeZoneSelected:
            (Get.arguments?['onTimeZoneSelected']
                as _i14.FutureSetTimeZoneCallback),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.timeZoneFirstView,
      bindings: [_i15.TimeZoneFirstBinding()],
      page: () => _i16.TimeZoneFirstView(
        key: (Get.arguments?['key'] as _i4.Key?),
        selectedTimeZone: (Get.arguments?['selectedTimeZone'] as String),
        onTimeZoneSelected:
            (Get.arguments?['onTimeZoneSelected']
                as _i14.FutureSetTimeZoneCallback),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.generalSettings,
      bindings: [_i17.DeviceGeneralSettingsBinding()],
      page: () => _i18.DeviceGeneralSettingsView(
        key: (Get.arguments?['key'] as _i4.Key?),
        ip: (Get.arguments?['ip'] as String),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.deviceReportIssueView,
      bindings: [_i19.DeviceReportIssueBinding()],
      page: () => _i20.DeviceReportIssueView(
        submitIssue: (Get.arguments?['submitIssue'] as SubmitIssue),
        key: (Get.arguments?['key'] as _i4.Key?),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.setLanguageView,
      bindings: [_i21.SetLanguageBinding()],
      page: () =>
          _i22.SetLanguageView(key: (Get.arguments?['key'] as _i4.Key?)),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.wifiDiscoveryView,
      bindings: [_i23.WifiDiscoveryBinding()],
      page: () => _i24.WifiDiscoveryView(
        device: (Get.arguments?['device'] as _i25.ScanResult),
        key: (Get.arguments?['key'] as _i4.Key?),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.bleDiscoveryView,
      bindings: [_i26.BleDiscoveryBinding()],
      page: () =>
          _i27.BleDiscoveryView(key: (Get.arguments?['key'] as _i4.Key?)),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.passwordInputView,
      bindings: [_i28.PasswordInputBinding()],
      page: () => _i29.PasswordInputView(
        key: (Get.arguments?['key'] as _i4.Key?),
        passwordCallback:
            (Get.arguments?['passwordCallback'] as _i30.OutValueCallback),
      ),
      transition: Transition.fadeIn,
      fullscreenDialog: true,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: false,
    ),
    GetPage<dynamic>(
      name: RouteNames.connectingDeviceStatusView,
      bindings: [_i31.ConnectingDeviceStatusBinding()],
      page: () => _i32.ConnectingDeviceStatusView(
        key: (Get.arguments?['key'] as _i4.Key?),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.gCastInfoView,
      bindings: [_i33.GCastInfoBinding()],
      page: () => _i34.GCastInfoView(
        key: (Get.arguments?['key'] as _i4.Key?),
        onSkip: (Get.arguments?['onSkip'] as _i35.VoidCallback),
        onGotoHomeApp: (Get.arguments?['onGotoHomeApp'] as _i35.VoidCallback),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.setGCastView,
      bindings: [_i36.SetGCastBinding()],
      page: () => _i37.SetGCastView(
        key: (Get.arguments?['key'] as _i4.Key?),
        onSkip: (Get.arguments?['onSkip'] as _i35.VoidCallback),
        onAccept: (Get.arguments?['onAccept'] as _i35.VoidCallback),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
    GetPage<dynamic>(
      name: RouteNames.gcastReady,
      bindings: [_i38.GCastReadyBinding()],
      page: () => _i39.GCastReadyView(
        key: (Get.arguments?['key'] as _i4.Key?),
        deviceIP: (Get.arguments?['deviceIP'] as String),
      ),
      fullscreenDialog: false,
      preventDuplicates: true,
      showCupertinoParallax: true,
      opaque: true,
    ),
  ];
}

class RouteNames {
  static const homeView = '/home_view';

  static const testPageView = '/test_page_view';

  static const soundSettingsView = '/sound_settings_view';

  static const deviceUpgradingView = '/device_upgrading_view';

  static const playView = '/play_view';

  static const timeZoneSecondView = '/time_zone_second_view';

  static const timeZoneFirstView = '/time_zone_first_view';

  static const generalSettings = '/general_settings';

  static const deviceReportIssueView = '/device_report_issue_view';

  static const setLanguageView = '/set_language_view';

  static const wifiDiscoveryView = '/wifi_discovery_view';

  static const bleDiscoveryView = '/ble_discovery_view';

  static const passwordInputView = '/password_input_view';

  static const connectingDeviceStatusView = '/connecting_device_status_view';

  static const gCastInfoView = '/g_cast_info_view';

  static const setGCastView = '/set_g_cast_view';

  static const gcastReady = '/gcast_ready';
}

class HomeViewTypedRoute extends _i40.TypedTlGetRouter {
  HomeViewTypedRoute({_i4.Key? key}) {
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.homeView;
}

class TestPageViewTypedRoute extends _i40.TypedTlGetRouter {
  TestPageViewTypedRoute({_i4.Key? key}) {
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.testPageView;
}

class SoundSettingsViewTypedRoute extends _i40.TypedTlGetRouter {
  SoundSettingsViewTypedRoute({required String ip, _i4.Key? key}) {
    super.arguments.addAll({'ip': ip});
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.soundSettingsView;
}

class DeviceUpgradingViewTypedRoute extends _i40.TypedTlGetRouter {
  DeviceUpgradingViewTypedRoute({_i4.Key? key, required String deviceIP}) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'deviceIP': deviceIP});
  }

  @override
  final routeName = RouteNames.deviceUpgradingView;
}

class PlayViewTypedRoute extends _i40.TypedTlGetRouter {
  PlayViewTypedRoute({_i4.Key? key, required String deviceIP}) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'deviceIP': deviceIP});
  }

  @override
  final routeName = RouteNames.playView;
}

class TimeZoneSecondViewTypedRoute extends _i40.TypedTlGetRouter {
  TimeZoneSecondViewTypedRoute({
    _i4.Key? key,
    required int index,
    required String selectedTimeZone,
    required _i14.FutureSetTimeZoneCallback onTimeZoneSelected,
  }) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'index': index});
    super.arguments.addAll({'selectedTimeZone': selectedTimeZone});
    super.arguments.addAll({'onTimeZoneSelected': onTimeZoneSelected});
  }

  @override
  final routeName = RouteNames.timeZoneSecondView;
}

class TimeZoneFirstViewTypedRoute extends _i40.TypedTlGetRouter {
  TimeZoneFirstViewTypedRoute({
    _i4.Key? key,
    required String selectedTimeZone,
    required _i14.FutureSetTimeZoneCallback onTimeZoneSelected,
  }) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'selectedTimeZone': selectedTimeZone});
    super.arguments.addAll({'onTimeZoneSelected': onTimeZoneSelected});
  }

  @override
  final routeName = RouteNames.timeZoneFirstView;
}

class GeneralSettingsTypedRoute extends _i40.TypedTlGetRouter {
  GeneralSettingsTypedRoute({_i4.Key? key, required String ip}) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'ip': ip});
  }

  @override
  final routeName = RouteNames.generalSettings;
}

class DeviceReportIssueViewTypedRoute extends _i40.TypedTlGetRouter {
  DeviceReportIssueViewTypedRoute({
    required SubmitIssue submitIssue,
    _i4.Key? key,
  }) {
    super.arguments.addAll({'submitIssue': submitIssue});
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.deviceReportIssueView;
}

class SetLanguageViewTypedRoute extends _i40.TypedTlGetRouter {
  SetLanguageViewTypedRoute({_i4.Key? key}) {
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.setLanguageView;
}

class WifiDiscoveryViewTypedRoute extends _i40.TypedTlGetRouter {
  WifiDiscoveryViewTypedRoute({required _i25.ScanResult device, _i4.Key? key}) {
    super.arguments.addAll({'device': device});
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.wifiDiscoveryView;
}

class BleDiscoveryViewTypedRoute extends _i40.TypedTlGetRouter {
  BleDiscoveryViewTypedRoute({_i4.Key? key}) {
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.bleDiscoveryView;
}

class PasswordInputViewTypedRoute extends _i40.TypedTlGetRouter {
  PasswordInputViewTypedRoute({
    _i4.Key? key,
    required _i30.OutValueCallback passwordCallback,
  }) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'passwordCallback': passwordCallback});
  }

  @override
  final routeName = RouteNames.passwordInputView;
}

class ConnectingDeviceStatusViewTypedRoute extends _i40.TypedTlGetRouter {
  ConnectingDeviceStatusViewTypedRoute({_i4.Key? key}) {
    super.arguments.addAll({'key': key});
  }

  @override
  final routeName = RouteNames.connectingDeviceStatusView;
}

class GCastInfoViewTypedRoute extends _i40.TypedTlGetRouter {
  GCastInfoViewTypedRoute({
    _i4.Key? key,
    required _i35.VoidCallback onSkip,
    required _i35.VoidCallback onGotoHomeApp,
  }) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'onSkip': onSkip});
    super.arguments.addAll({'onGotoHomeApp': onGotoHomeApp});
  }

  @override
  final routeName = RouteNames.gCastInfoView;
}

class SetGCastViewTypedRoute extends _i40.TypedTlGetRouter {
  SetGCastViewTypedRoute({
    _i4.Key? key,
    required _i35.VoidCallback onSkip,
    required _i35.VoidCallback onAccept,
  }) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'onSkip': onSkip});
    super.arguments.addAll({'onAccept': onAccept});
  }

  @override
  final routeName = RouteNames.setGCastView;
}

class GcastReadyTypedRoute extends _i40.TypedTlGetRouter {
  GcastReadyTypedRoute({_i4.Key? key, required String deviceIP}) {
    super.arguments.addAll({'key': key});
    super.arguments.addAll({'deviceIP': deviceIP});
  }

  @override
  final routeName = RouteNames.gcastReady;
}

typedef SubmitIssue = Future<bool> Function({required String issue});
