import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import AuthClass from '../api/auth.js';
import router from '@/router/index.js';

export const useUserStore = defineStore('user', {
	state: () => ({
		// 用户信息
		userInfo: {
			id: '',
			name: '',
			token: '',
		},
		// 是否显示弹窗
		showDialog: false,
		// 0: public station页面 1: private station页面
		inPageMode: 0,
		// 音乐列表
		musicSongs: [],
		// 自动登录状态锁
		autoLoginLock: false,
	}),

	actions: {
		// 设置用户信息
		setUserInfo(userInfo) {
			this.userInfo = userInfo;
		},

		// 自动登录
		async autoLogin() {
			// 获取用户信息
			const remember = AuthClass.getRmember();
			if (!remember) return console.log('不自动登录');

			// 获取用户登录信息
			const loginInfo = AuthClass.getUserLogin();
			if (loginInfo) {
				// 如果存在登录信息，则使用登录信息进行登录
				const res = await AuthClass.loginFun(loginInfo.username, loginInfo.password).catch(err => {
					console.log('自动登录失败', err);
				})

				if (res.status === 0) {
					console.log('自动登录成功', res);
					this.setUserInfo({
						id: res.id,
						name: res.name,
						token: res.token,
					});
					AuthClass.saveUserLogin({
						username: loginInfo.username,
						password: loginInfo.password,
					});
					AuthClass.saveUserInfo({
						name: res.name,
						id: res.id,
						token: res.token,
					});
					ElMessage({
						message: 'Login successful',
						type: 'success',
					});
					console.log('自动登录前往home');
					router.push('/home');
				}
			} else {
				// 登录失败，打印失败日志
				console.log('自动登录失败,用户信息是错误', loginInfo);
			}
		},
	}
});