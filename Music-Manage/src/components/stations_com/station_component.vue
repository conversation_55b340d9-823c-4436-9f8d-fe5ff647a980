<template>
    <!-- 歌单页面 -->
    <div class="station-body">
        <!-- 标题 -->
        <div class="stations-title">{{ userStore.inPageMode == 0? 'Public Station' : 'My Station' }}</div>
        <!-- 选择框 -->
        <div class="select-container" v-if="userStore.inPageMode == 0">
            <!-- genre类型 -->
            <div class="genre-container">
                <el-select 
                    size="large"
                    placeholder="Search Genre" 
                    effect="dark"
                    v-model="genreValue" 
                    style="width: 220px;"
                    @change="getFilterSongs()"
                    filterable
                    clearable
                >
                    <el-option v-for="item in genreList" :key="item.id" :label="item.name" :value="item.name" />
                </el-select>
            </div>
            <!-- 语言类型 -->
            <div class="language-container">
                <el-select 
                    size="large"
                    placeholder="Search Language"
                    effect="dark"
                    v-model="languageValue" 
                    style="width: 220px;"
                    @change="getFilterSongs()"
                    filterable
                    clearable
                >
                    <el-option v-for="item in languageList" :key="item.id" :label="item.name" :value="item.name" />
                </el-select>
            </div>
            <!-- 国家类型 -->
            <div class="country-container">
                <el-select 
                    size="large"
                    placeholder="Search Country" 
                    effect="dark"
                    v-model="countryValue" 
                    style="width: 220px;"
                    @change="getFilterSongs()"
                    filterable
                    clearable
                >
                    <el-option v-for="item in countryList" :key="item.id" :label="item.name" :value="item.name" />
                </el-select>
            </div>
            <div class="station-clear-button" @click="clearFilter()">
                Clear
            </div>
        </div>
        <!-- 歌曲内容主体 -->
        <div class="stations-container" :class="store.state.inPageMode == 1? 'stations-container-mystation-page' : ''">
            <!-- 操作栏 -->
            <div class="stations-edit-container">
                <div class="stations-edit-column" v-if="musicSongs.length > 0" :class="showColumn ? 'is-show-column' : ''" @click="showColumn = !showColumn">
                    <el-icon size="18px" style="margin-right: 6px;" v-if="showColumn"><Expand /></el-icon>
                    <el-icon size="18px" style="margin-right: 6px;" v-else><Grid /></el-icon>
                    <div> {{ showColumn? 'Column' : 'Grid' }}</div>
                </div>
            </div>
            <!-- 歌曲列表 -->
            <div class="stations-container-scroll" :class="showColumn?'stations-container-scroll-column' : ''" v-if="musicSongs.length > 0">
                <div class="station-item" v-for="(item, index) in musicSongs" :key="index">
                    <el-image
                        :src="item.image_url"
                        :preview-src-list="[item.image_url]"
                        fit="cover"
                        class="station-item-image"
                    >
                        <template #error>
                            <div class="station-image-error">
                                <el-icon><Picture /></el-icon>
                            </div>
                        </template>
                    </el-image>
                    <div class="station-item-title">
                        <span>{{ index + 1 }}. </span>
                        <span>{{ item.title }}</span>
                    </div>
                    <div class="station-item-more" @click="showItemInfo(item)">
                        <el-icon><MoreFilled /></el-icon>
                    </div>
                </div>
            </div>
            <!-- 空data -->
            <div v-else-if="musicSongs.length === 0 && noMusicSongs" class="station-content-empty">
                <el-empty :image-size="200" />
            </div>
            <!-- 加载 -->
            <template v-else>
                <div class="station-content-loading">
                    <el-icon class="is-loading" >
                        <Loading style="transform: scale(2);"/>
                    </el-icon>
                    <div class="loading-text" style="margin-top: 12px;">
                        Loading...
                    </div>
                </div>
            </template>
        </div>
        <StationDilig/>
    </div>
</template>

<script setup>
    import { onBeforeMount, ref } from 'vue';
    import { stationsSotre } from './station_store.js'
    import { Loading, Picture, MoreFilled, Expand, Grid } from '@element-plus/icons-vue';
    import StationClass from '../../api/station.js';
    import LocalClass from '../../api/local.js';
    import StationDilig from '../dialog/station_dialog.vue';
    import { useUserStore } from '../../store/index.js';

    const userStore = useUserStore();

    const {
        FILTER_TYPES,
        genreValue, 
        genreList,
        languageValue,
        languageList,
        countryValue,
        countryList,
        musicSongs,
        noMusicSongs,
        clearFilter,
        getFilterSongs,
    } = stationsSotre();

    const SHOW_COLUMN_KEY = 'show-column';

    onBeforeMount(async () => {
        const SHOW_COLUMN = LocalClass.getItem(SHOW_COLUMN_KEY);
        showColumn.value = SHOW_COLUMN;

        try {
            const [genreRes, languageRes, countryRes] = await Promise.all([
                StationClass.getFilterList(FILTER_TYPES.GENRE),
                StationClass.getFilterList(FILTER_TYPES.LANGUAGE),
                StationClass.getFilterList(FILTER_TYPES.COUNTRY)
            ]);
            genreList.push(...genreRes['genres']);
            languageList.push(...languageRes['language']);
            countryList.push(...countryRes['country']);
        } catch (error) {
            console.error(error);
        }
    });

    const showColumn = ref(false); // 显示列

    // watch(showColumn, (newValue, oldValue) => {
    //     console.log(newValue, oldValue);
    //     LocalClass.setItem(SHOW_COLUMN_KEY, newValue);
    // })

    const showItemInfo = (item) => {
        console.log(item);
        userStore.showDialog = true;
    }

</script>

<style lang="scss" scoped>
    $title-height: 60px;
    $search-height: 40px;
    $this-color: #606266;

    .station-body {
        height: calc(100vh - ($title-height + 20px));
        @include flex-column;
        background: $page-bg-color;
        padding: 20px;
        color: $text-color;

        .stations-title {
            font-size: 19px;
            height: $title-height;
            font-weight: bold;
        }

        .select-container {
            height: $search-height;
            @include flex-row;

            @media (max-width: 910px) {
                flex-direction: column !important;
                
                div {
                    width: 100%;
                    margin-right: 0;
                    margin-bottom: 8px;  // 底部间距
                }
            }

            div {
                margin-right: 10px;
            }

            :deep(.el-select) {

                .el-select__wrapper{
                    background-color: $page-bg-color;
                    box-shadow:  0 0 0 1px #cccccc7e;

                    .is-transparent {
                        color: $text-sec-color !important;
                    }

                    .el-select__selected-item {
                        color: blue-color();
                    }
                }


                .is-focused {
                    box-shadow:  0 0 0 1px blue-color();
                }
            }

            .station-clear-button {
                width: 100px;
                height: 40px;
                text-align: center;
                line-height: 40px;
                box-shadow: 0 0 0 1px rgba(204, 204, 204, 0.5);
                border-radius: 4px;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                cursor: pointer;
                color: #a2a2a2;
                background-color: $page-bg-color;
                font-size: 15px;

                &:hover {
                    color: #fff;
                    background-color: blue-color(.1);
                    box-shadow: 0 0 0 1px blue-color();
                }

                &:active {
                    color: #fff;
                    transform: scale(.98);
                    transition: transform 0.0.5s ease-in;
                }
            }
        }

        .stations-container-mystation-page {
            height: calc(100vh - ($title-height + $search-height + 110px)) !important;
            margin-top: 0px !important;
        }

        .stations-container {
            background-color: $second-color;
            padding: 10px 20px 20px 20px;
            margin-top: 20px;
            border-radius: 10px;
            height: calc(100vh - ($title-height + $search-height + 160px + 10px));
            overflow: hidden;
            @include flex-column;

            @media (max-width: 910px) {
                margin-top: 48px * 4;
                height: calc(100vh - ($title-height + (48px * 4) + 160px + 40px));
            }

            .stations-edit-container {
                height: 40px;
                margin-bottom: 10px;
                padding-right: 18px;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .stations-edit-column {
                    // width: 75px;
                    @include flex-row();
                    align-items: center;
                    background-color: $page-bg-color;
                    padding: 8px 10px;
                    border-radius: 40px;
                    -webkit-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                    cursor: pointer;
                    font-size: 14px;
                    box-shadow: 0 0 0 1px blue-color(1);

                    &:hover {
                        box-shadow: 0 0 0 1px blue-color(.5);
                    }

                    &:active {
                        opacity: .3;
                        box-shadow: 0 0 0 1px blue-color(.8);
                    }

                    @media (max-width: 910px) {
                        display: none !important;
                    }
                }

                .is-show-column {
                    opacity: 1;
                    box-shadow: 0 0 0 1px blue-color();
                }
            }

            .station-content-loading {
                @include flex-center();
                width: 100%;
                flex-direction: column;
                flex: 1;
            }

            .station-content-empty {
                @include flex-center();
                width: 100%;
                flex: 1;
            }

            .stations-container-scroll-column {
                grid-template-columns: initial !important;
            }

            .stations-container-scroll {
                flex: 1;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
                grid-auto-rows: minmax(150px, 150px);
                grid-gap: 15px;
                overflow-y: scroll;
                overflow-x: hidden;
                padding-right: 10px;

                &::-webkit-scrollbar {
                    width: 8px;
                    background: transparent;
                }

                &::-webkit-scrollbar-thumb {
                    background-color: rgba(126, 197, 241, 0.5);
                    border-radius: 6px;

                    &:hover {
                        background-color: rgba(126, 197, 241, 1);
                    }
                }

                @media (max-width: 910px) {
                    grid-template-columns: repeat(1, 1fr);
                    grid-gap: 10px;
                }

                .station-item {
                    border-radius: 10px;
                    background-color: $page-bg-color;
                    padding: 15px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    position: relative;

                    .station-item-image {
                        width: 60px;
                        height: 60px;
                        border-radius: 6px;
                        margin-bottom: 20px;
                    }

                    .station-image-error {
                        @include flex-center();
                        width: 60px;
                        height: 60px;
                        border-radius: 6px;
                        margin-bottom: 10px;
                        background: #ffffff0f;
                    }

                    .station-item-title {
                        word-break: break-word;
                        font-size: 14px;
                        line-height: 1.4;
                        height: 40px;
                        overflow: hidden;
                    }

                    .station-item-more {
                        position: absolute;
                        @include flex-center();
                        width: 30px;
                        height: 30px;
                        top: 10px;
                        right: 10px;
                        font-size: 14px;
                        border-radius: 10px;
                        color: #ccc;
                        background-color: rgba(126, 197, 241, 0.1);
                        transform: rotate(90deg);

                        &:hover {
                            background-color: rgba(126, 197, 241, 0.3);
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
</style>