import AxiosClass from '../utils/AxiosClass.js';
import Local from '../api/local.js'

export default class StationClass {
    constructor() {}

    // 获取电台完整信息
    static getStationInfo = async (songId) => {
        // 从本地存储获取用户信息
        const userInfo = Local.getItem('userInfo');
        let data = {
            sid: songId,
            token: userInfo?.token || ''
        };
        return new Promise(async (resolve, reject) => {
            return AxiosClass.post('station/info', data).then(res => {
                resolve(res);
            }).catch(err => {
                reject(err);
            })
        })
    }
    
    // 获取过滤列表
    static getFilterList = async (action) => {
        return new Promise(async (resolve, reject) => {
            return AxiosClass.get('/' + action).then(res => {
                resolve(res);
            }).catch(err => {
                reject(err);
            })
        })
    }

    // 获取所有电台列表
    static getQuery = async () => {
        return new Promise(async (resolve, reject) => {
            console.log('发送 station/query 请求');
            return AxiosClass.post('station/query', { 'count': 99999 }).then(res => {
                console.log('station/query 响应:', res);
                resolve(res);
            }).catch(err => {
                console.error('station/query 错误:', err);
                reject(err);
            })
        })
    }

    static getQueryOnFilter = async (genreId, languageId, countryId) => {
        const data = {
            count: 99999,
            genre: genreId,
            language: languageId,
            country: countryId
        }
        return new Promise(async (resolve, reject) => {
            return AxiosClass.post('station/query', data).then(res => {
                resolve(res);
            }).catch(err => {
                reject(err);
            })
        })
    }

    static setAllSongsAndPublic = async (musicSongs) => {
        const PUBLIC_STAIONS_KYENAME = 'public_stations';

        if (Local.getSession(PUBLIC_STAIONS_KYENAME)) {
            console.log('缓存中获取歌曲数据');
            musicSongs.push(...Local.getSession(PUBLIC_STAIONS_KYENAME));
        } else {
            console.log('请求中获取歌曲数据');
            try {
                const res = await StationClass.getQuery();
                console.log('获取到歌曲数据:', res);
                musicSongs.push(...res.stations);
                Local.setSession(PUBLIC_STAIONS_KYENAME, res.stations); // 缓存30天
            } catch (error) {
                console.error('获取歌曲数据失败:', error);
            }
        }
    }
}