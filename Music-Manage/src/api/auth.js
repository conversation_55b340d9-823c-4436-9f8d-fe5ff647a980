import AxiosClass from '../utils/AxiosClass.js';
import { RequestController } from '../utils/throttle.js';
import { encodeMd5 } from '../utils/utils.js';
import Local from '../api/local.js';

const requestControl = new RequestController();

export default class AuthClass {
    constructor() {}

    /**
     * 登录认证函数
     * 
     * 此函数负责向服务器发送用户登录请求它接受用户名和密码作为参数，
     * 并使用Axios库发送POST请求到指定的API端点登录成功或失败后，
     * 它会根据服务器返回的状态码打印相应的日志信息如果在请求过程中
     * 发生错误，它会捕获并打印错误信息
     * 
     * @param {string} mail 邮箱
     * @param {string} password 密码
     */
    static loginFun = (mail, password) => {
        return new Promise((resolve, reject) => {
            // 请求监听器，用于处理登录请求
            return requestControl.request(async () => {
                // 发送POST请求到服务器的/user/login端点，并传递用户名和密码
                return AxiosClass.post('/user/login', {
                    mail,
                    password: encodeMd5(password)
                }).then(res => {
                    // 根据服务器返回的状态码判断登录是否成功
                    if (res.status === 0) {
                        // 登录成功，打印成功日志
                        resolve(res);
                    } 
                    // 登录失败，打印错误日志
                    reject(res);
                }).catch(err => {
                    // 请求过程中的错误，打印错误日志
                    reject(err);
                });
            });
        });
    };

    /**
     * 注册用户身份验证的函数。
     * 
     * @param {string} email - 用户的电子邮件地址，用于注册。
     * @param {string} username - 用户名，用于注册。
     * @param {string} password - 用户密码，用于注册。
     * @param {string} [desc='empty'] - 用户描述信息，默认值为 'empty'。
     * @returns {Promise} - 返回一个 Promise，表示异步操作的结果。
     *      该 Promise 由 requestListener 包装的异步函数返回。
     */
    static registerFun = (mail, username, password, desc = 'empty') => {
        return requestControl.request(async () => {
            // 发送 POST 请求到 '/user/register' 接口，传递用户注册信息
            return AxiosClass.post('/user/register', {
                mail,
                password: encodeMd5(password),
                username,
                desc
            }).then(res => {
                // 根据响应状态判断注册是否成功，并打印相应日志
                if (res.status === 0) {
                    console.log('Register successful:', res);
                } else {
                    console.error('Register failed:', res);
                }
            }).catch(err => {
                // 捕获请求过程中的错误，并打印错误日志
                console.log('register error:', err);
            });
        });
    };

    static saveUserLogin = (loginInfo) => {
        Local.setItem(
            'loginInfo',
            loginInfo,
            // 30天过期
            60 * 24 * 30
        );
    };

    static getUserLogin = () => {
        return Local.getItem('loginInfo');
    }

    static removeUserLogin = () => {
        Local.removeItem(
            'loginInfo',
        );
    }

    static saveRmember = (val) => {
        Local.setItem('remember', val);
    }

    static getRmember = () => {
        try {
            return Local.getItem('remember');
        } catch (error) {
            return false;
        }
    }

    static saveUserInfo = (userInfo) => {
        console.log('保存用户信息');
        Local.setItem('userInfo', userInfo, 30);
        // 注意：这里不再直接操作 store，而是由调用方负责更新 store
        this.roundUserInfo();
    }

    static roundUserInfo = () => {
        const updateTime = 30 * 60 * 1000;
        console.log('注册' + (updateTime / 1000) + '秒的更新token定时器');

        const timer = setInterval(() => {
            try {
                const userInfo = Local.getItem('userInfo');
                // 注意：这里不再直接操作 store，需要在使用的地方手动更新
                console.log('已更新token', userInfo);
            } catch (error) {
                // 结束循环
                console.log(error);
                clearInterval(timer);
            }
        }, updateTime);
    }

}