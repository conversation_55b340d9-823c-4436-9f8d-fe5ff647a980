<template>
    <div class="homeBody">
        <div class="homeHeader">
            <el-row type="flex" justify="space-between">
                <div class="homeHeader-title">Rakoit Station</div>
                <div style="display: flex;justify-content: center;align-items: center;">
                    <el-avatar style="background-color: #67C23A; margin-right: 20px;" :style="{ marginLeft: 'auto' }">L</el-avatar>
                </div>
            </el-row>
        </div>

        <div class="content-wrapper">
            <!-- 侧边菜单栏 -->
            <div class="sidebar-container">
                <el-row>
                    <el-menu 
                        :collapse="isCollapsed"
                        @mouseenter="isCollapsed = false"
                        @mouseleave="isCollapsed = true"
                        @select="changeMenuSelect"
                        class="sidebar-menu" 
                        background-color="#2A2D3E" 
                        text-color="#959595" 
                        active-text-color="#ffffff"
                        default-active="1"
                    >
                        <el-menu-item index="1">
                            <el-icon><Headset /></el-icon>
                            <span>Public Station</span>
                        </el-menu-item>
                        <el-menu-item index="2">
                            <el-icon><Document /></el-icon>
                            <span>My Station</span>
                        </el-menu-item>
                        <el-menu-item index="3">
                            <el-icon><VideoPlay /></el-icon>
                            <span>Devices</span>
                        </el-menu-item>
                    </el-menu>
                </el-row>
            </div>
            
            <!-- 主内容区域 -->
            <div class="main-content">
                <component :is="currentComponent" />
            </div>
            
        </div>
    </div>
</template>

<script setup>
import { ref, shallowRef } from 'vue';
import { Headset, Document, VideoPlay } from '@element-plus/icons-vue';
import PublicStation from './stations_page/public_station.vue';
import MyStation from './stations_page/my_station.vue';
import Devices from './stations_page/devices.vue';
import { useUserStore } from '../store/index.js';

const userStore = useUserStore();
const isCollapsed = ref(true);

const menuMap = {
  '1': PublicStation,
  '2': MyStation,
  '3': Devices,
};

// 使用shallowRef优化性能
const currentComponent = shallowRef(PublicStation);

const changeMenuSelect = (index) => {
    currentComponent.value = menuMap[index];
    if (index == 1) {
        userStore.inPageMode = 0;
    } else if (index == 2) {
        userStore.inPageMode = 1;
    }
}

</script>

<style scoped lang="scss">

.homeBody {
    background: #F8F9FD;
    height: 100vh;

    .content-wrapper {
        display: flex;
        height: calc(100vh - 80px); // 扣除顶部导航栏高度
        overflow: hidden;
    }

    .sidebar-container {
        flex-shrink: 0; // 禁止侧边栏收缩
        height: 100%;
    }

    .homeHeader {
        height: 80px;
        background-color: #34384C;
        color: #FFF;
        box-shadow: 0 2px 5px 3px rgba(0, 0, 0, 0.15);

        .el-row {
            width: 100%;
            height: 100%;
        }

        .homeHeader-title {
            @include flex-center;
            margin-left: 20px;
            font-size: 22px;
            font-weight: bold;
        }
    }

    .sidebar-container {
        min-width: 50px;
        // max-width: 20vw;
        overflow: hidden;
    }

    .sidebar-menu {
        height: calc(100vh - 60px);
        border-right: none;

        :deep(.el-menu-item.is-active) {
            position: relative;
            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 100%;
                background-color: rgba(102, 182, 255, 1);
                border-radius: 1px;
            }
        }

        // 折叠状态样式
        &.el-menu--collapse {
            :deep(.el-menu-item.is-active) {
                &::before {
                    left: -1px; // 调整位置适应折叠状态
                }
            }
            :deep(.el-sub-menu__title span),
            :deep(.el-menu-item span) {
                opacity: 0;
                width: 0;
                padding: 0;
                overflow: hidden;
            }
        }
    }

    .main-content {
        display: flex;
        flex-direction: column;
        flex: 1;
        background-color: #f5f5f5;
    }
}
</style>