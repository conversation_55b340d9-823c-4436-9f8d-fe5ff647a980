<template>
  <div class="AppView">
    <router-view />
  </div>
</template>

<script setup>
  import { onBeforeMount } from 'vue';
  import Local from './api/local.js';
  import { useUserStore } from './store/index.js';

  const userStore = useUserStore();

  onBeforeMount(() => {
    // 初始化本地存储
    Local.init();

    userStore.autoLogin();
    
    // if (userInfo) {
    //   store.state.userInfo.id = userInfo.id;
    //   store.state.userInfo.name = userInfo.name;
    //   store.state.userInfo.token = userInfo.token;
    // }

    // if (remember) {
    //   console.log('直接登录');
    //   const loginInfo = AuthClass.getUserLogin(); 
    //   let userName = loginInfo.username;
    //   let passWord = loginInfo.password;
    //   console.log(userName, passWord);
    //   AuthClass.loginFun(userName, passWord).then((result) => {
    //     router.push({path: '/home'})
    //     AuthClass.saveUserInfo({
    //         name: result.name,
    //         id: result.id,
    //         token: result.token,
    //     })
    //     ElMessage({
    //         message: 'Automatic login successful!',
    //         type: 'success',
    //     });
    //   }).catch((err) => {
    //     console.error('请求失败', err);
    //     ElMessage({
    //         message: 'Login failed. Error message:' + err.msg,
    //         type: 'error',
    //     });
    //   });
    // }
  });
</script>

<style lang="scss">
  * {
    margin: 0;
    padding: 0;
  }

  .AppView {
    background: $bg-color;
  }

  .el-popper {
    background-color: $page-bg-color !important;
    box-shadow:  0 0 0 1px $page-bg-color !important;

    ::before {
      background-color: $page-bg-color !important;
    }

    .el-scrollbar__thumb {
      background-color: blue-color(0.5) !important;
        
      &:hover {
        background-color: blue-color() !important;
      }
    }

    .el-select-dropdown__item {
        padding: 0 10px;
        margin: 5px 2px;
        border-radius: 5px;
        
        &.is-hovering {
          background-color: blue-color(0.1) !important;
          box-shadow:  0 0 0 1px blue-color() !important;
          color: white;
          opacity: .6;
        }

        &.is-selected {
          background-color: blue-color(0.1) !important;
          box-shadow:  0 0 0 1px blue-color() !important;
          color: white;
        }
    }
  }
</style>
